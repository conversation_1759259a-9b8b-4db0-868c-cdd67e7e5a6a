#!/bin/bash

# B端智能助手-APA 快速启动脚本

echo "🚀 B端智能助手-APA 快速启动向导"
echo "=================================="

# 检查操作系统
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    CYGWIN*)    MACHINE=Cygwin;;
    MINGW*)     MACHINE=MinGw;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

echo "检测到操作系统: ${MACHINE}"

# 函数：安装客户端插件
install_client() {
    echo ""
    echo "📱 安装客户端Chrome插件"
    echo "========================"
    echo "1. 打开Chrome浏览器"
    echo "2. 访问 chrome://extensions/"
    echo "3. 开启右上角'开发者模式'开关"
    echo "4. 点击'加载已解压的扩展程序'"
    echo "5. 选择项目中的 client/ 目录"
    echo ""
    echo "安装完成后，访问以下测试页面："
    
    if [[ "$MACHINE" == "Mac" ]]; then
        echo "正在打开测试页面..."
        open "client/test.html"
    elif [[ "$MACHINE" == "Linux" ]]; then
        echo "请手动打开: $(pwd)/client/test.html"
    else
        echo "请手动打开: $(pwd)/client/test.html"
    fi
    
    echo ""
    echo "✅ 如果看到右侧悬浮机器人图标，说明安装成功！"
}

# 函数：初始化服务端
init_server() {
    echo ""
    echo "🚀 初始化服务端项目"
    echo "==================="
    
    # 检查Java环境
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
        echo "检测到Java版本: $JAVA_VERSION"
    else
        echo "❌ 未检测到Java环境，请先安装Java 17+"
        echo "下载地址: https://adoptium.net/"
        return 1
    fi
    
    # 检查Maven环境
    if command -v mvn &> /dev/null; then
        MVN_VERSION=$(mvn -version | head -n 1)
        echo "检测到Maven: $MVN_VERSION"
    else
        echo "❌ 未检测到Maven环境，请先安装Maven"
        echo "下载地址: https://maven.apache.org/download.cgi"
        return 1
    fi
    
    echo ""
    echo "🔄 服务端项目当前处于规划阶段"
    echo "预计开发时间: 8周"
    echo "主要技术栈: Java + Spring Boot + Selenium + Redis"
    echo ""
    echo "📋 开发计划:"
    echo "  第1-2周: 基础架构搭建"
    echo "  第3-4周: RPA执行引擎"
    echo "  第5-6周: 业务逻辑实现"
    echo "  第7-8周: 优化和测试"
}

# 函数：查看文档
view_docs() {
    echo ""
    echo "📚 项目文档"
    echo "==========="
    echo "主要文档文件:"
    echo "  📄 docs/README.md - 项目详细说明"
    echo "  📄 docs/INSTALL.md - 安装使用指南"
    echo "  📄 docs/DEPLOYMENT.md - 部署运维指南"
    echo "  📄 docs/DEMO.md - 功能演示说明"
    echo "  📄 docs/Doc/ - 原始设计文档"
    echo ""
    echo "客户端文档:"
    echo "  📄 client/README.md - 客户端插件说明"
    echo ""
    echo "服务端文档:"
    echo "  📄 server/README.md - 服务端开发计划"
    echo ""
    
    if [[ "$MACHINE" == "Mac" ]]; then
        echo "正在打开主要文档..."
        open "docs/README.md"
    else
        echo "请手动查看文档文件"
    fi
}

# 函数：项目状态检查
check_status() {
    echo ""
    echo "📊 项目状态检查"
    echo "==============="
    
    # 检查客户端文件
    echo "客户端插件:"
    if [[ -f "client/manifest.json" ]]; then
        echo "  ✅ manifest.json - 插件配置文件"
    else
        echo "  ❌ manifest.json - 缺失"
    fi
    
    if [[ -f "client/background.js" ]]; then
        echo "  ✅ background.js - 后台服务脚本"
    else
        echo "  ❌ background.js - 缺失"
    fi
    
    if [[ -f "client/content.js" ]]; then
        echo "  ✅ content.js - 内容注入脚本"
    else
        echo "  ❌ content.js - 缺失"
    fi
    
    if [[ -d "client/icons" ]]; then
        ICON_COUNT=$(ls client/icons/*.png 2>/dev/null | wc -l)
        echo "  ✅ icons/ - 图标文件 ($ICON_COUNT 个)"
    else
        echo "  ❌ icons/ - 缺失"
    fi
    
    # 检查服务端结构
    echo ""
    echo "服务端项目:"
    if [[ -d "server/src" ]]; then
        echo "  ✅ src/ - 源代码目录结构"
    else
        echo "  ❌ src/ - 缺失"
    fi
    
    if [[ -f "server/README.md" ]]; then
        echo "  ✅ README.md - 开发计划文档"
    else
        echo "  ❌ README.md - 缺失"
    fi
    
    # 检查文档
    echo ""
    echo "项目文档:"
    if [[ -d "docs" ]]; then
        DOC_COUNT=$(ls docs/*.md 2>/dev/null | wc -l)
        echo "  ✅ docs/ - 文档目录 ($DOC_COUNT 个文档)"
    else
        echo "  ❌ docs/ - 缺失"
    fi
}

# 主菜单
show_menu() {
    echo ""
    echo "请选择操作:"
    echo "1. 📱 安装客户端Chrome插件"
    echo "2. 🚀 查看服务端开发计划"
    echo "3. 📚 查看项目文档"
    echo "4. 📊 检查项目状态"
    echo "5. 🔧 生成图标文件"
    echo "6. ❌ 退出"
    echo ""
    read -p "请输入选项 (1-6): " choice
    
    case $choice in
        1)
            install_client
            ;;
        2)
            init_server
            ;;
        3)
            view_docs
            ;;
        4)
            check_status
            ;;
        5)
            echo "正在生成图标文件..."
            python3 generate_icons.py
            ;;
        6)
            echo "👋 感谢使用 B端智能助手-APA！"
            exit 0
            ;;
        *)
            echo "❌ 无效选项，请重新选择"
            ;;
    esac
}

# 主程序
main() {
    # 检查是否在项目根目录
    if [[ ! -f "package.json" ]] || [[ ! -d "client" ]] || [[ ! -d "server" ]]; then
        echo "❌ 请在项目根目录运行此脚本"
        echo "项目根目录应包含: package.json, client/, server/, docs/"
        exit 1
    fi
    
    # 显示项目信息
    echo ""
    echo "📁 当前目录: $(pwd)"
    echo "📦 项目版本: $(grep '"version"' package.json | cut -d'"' -f4)"
    echo ""
    
    # 循环显示菜单
    while true; do
        show_menu
        echo ""
        read -p "按回车键继续..."
    done
}

# 运行主程序
main
