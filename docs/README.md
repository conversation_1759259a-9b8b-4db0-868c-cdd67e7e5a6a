# B端智能助手-APA (Automated Process Assistant)

## 项目简介

B端智能助手-APA是一个Chrome浏览器插件，与服务端相结合实现RPA（机器人流程自动化）功能。主要解决遗留系统API开放度低、系统改造成本高等问题，通过浏览器插件的形式提供智能化的任务自动化服务。

## 核心功能

### 1. 悬浮图标
- 在特定目标系统域名下自动显示悬浮图标
- 点击图标可展开/收起助手面板
- 默认吸附在浏览器右侧

### 2. 插件主界面
包含三个主要Tab页：

#### 辅助任务
- **发起新任务**: 支持多种任务类型，Excel模板导入
- **任务列表**: 查看任务状态、历史记录

#### 智能对话
- 聊天对话界面，支持自然语言交互
- 快捷指令功能
- 流式对话支持

#### 设置
- 服务器地址配置
- 代理站点管理
- 通知设置

### 3. 核心业务逻辑
- **登录态检测**: 自动检测目标系统登录状态
- **Cookie采集**: 自动采集并上报登录凭证
- **WebSocket通信**: 实时消息推送和状态更新
- **任务管理**: HTTP协议的任务创建和查询

## 技术架构

### 客户端插件 (C-Assistant)
- **技术栈**: Chrome Extension API + JavaScript
- **职责**: 
  - 捕获用户会话（Cookies/Token）
  - 注入监控脚本
  - 转发数据
  - 用户界面交互

### 服务端 (S-Assistant)
- **技术栈**: Java + Redis + 代理IP池
- **职责**:
  - 接收任务
  - 模拟请求
  - 调度任务队列
  - 返回结果

## 安装使用

### 1. 开发模式安装
1. 打开Chrome浏览器
2. 进入扩展程序管理页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目根目录

### 2. 配置设置
1. 点击插件图标打开popup
2. 点击"设置"进入配置页面
3. 配置服务器地址（默认: ws://localhost:8080）
4. 配置目标站点和SSO地址

### 3. 使用流程
1. 访问目标系统网站
2. 确保已登录目标系统
3. 点击右侧悬浮图标打开助手
4. 选择相应功能进行操作

## 文件结构

```
BAssitant/
├── manifest.json          # 插件配置文件
├── background.js          # 后台脚本
├── content.js            # 内容脚本
├── content.css           # 样式文件
├── popup.html            # 弹出页面
├── popup.js              # 弹出页面脚本
├── icons/                # 图标文件夹
├── assets/               # 资源文件夹
├── Doc/                  # 设计文档
│   └── B端智能助手-APA.pdf
└── README.md             # 说明文档
```

## 主要组件说明

### manifest.json
定义插件的基本信息、权限和资源配置。

### background.js
- 处理插件生命周期事件
- 管理WebSocket连接
- 处理跨页面消息通信
- Cookie管理和登录状态检测

### content.js
- 注入页面悬浮图标
- 创建助手界面
- 处理用户交互
- 与background脚本通信

### content.css
定义助手界面的样式，确保在各种网站中都能正常显示。

## 开发说明

### 权限说明
- `activeTab`: 访问当前活动标签页
- `storage`: 本地存储配置信息
- `cookies`: 读取网站Cookie
- `tabs`: 标签页管理
- `scripting`: 脚本注入

### 通信协议
- **HTTP**: 任务提交和查询
- **WebSocket**: 实时消息推送
- **JSON**: 数据传输格式

### 扩展开发
1. 修改`content.js`添加新的UI组件
2. 在`background.js`中添加新的消息处理逻辑
3. 更新`content.css`添加样式
4. 在`manifest.json`中声明新的权限（如需要）

## 注意事项

1. **安全合规**: 确保Cookie采集和数据传输符合安全规范
2. **并发处理**: 注意多个Cookie并发执行可能的冲突
3. **错误处理**: 完善任务执行中断和人工交互机制
4. **兼容性**: 确保在不同网站和浏览器版本中的兼容性

## 后续优化

1. 添加更多任务类型支持
2. 优化UI/UX体验
3. 增强错误处理和日志记录
4. 添加数据统计和分析功能
5. 支持更多浏览器

## 版本信息

- 当前版本: 1.0.0
- 最后更新: 2024-01-15

## 联系方式

如有问题或建议，请联系开发团队。
