# B端智能助手-APA 安装使用指南

## 快速开始

### 1. 安装Chrome插件

#### 方法一：开发者模式安装（推荐）
1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/` 并回车
3. 在右上角开启"开发者模式"开关
4. 点击"加载已解压的扩展程序"按钮
5. 选择项目根目录（包含manifest.json的文件夹）
6. 插件安装成功后会在扩展程序列表中显示

#### 方法二：打包安装
1. 在Chrome扩展程序页面点击"打包扩展程序"
2. 选择项目根目录
3. 生成.crx文件后拖拽到Chrome中安装

### 2. 测试插件功能

#### 使用测试页面
1. 在浏览器中打开项目中的 `test.html` 文件
2. 页面加载后，右侧应该出现智能助手悬浮图标 🤖
3. 点击悬浮图标打开助手面板

#### 在实际网站测试
1. 访问任何包含 "erp"、"admin"、"manage"、"system" 关键词的网站
2. 插件会自动检测并显示悬浮图标
3. 点击图标体验完整功能

### 3. 配置设置

#### 基本配置
1. 点击Chrome工具栏中的插件图标
2. 在弹出窗口中点击"设置"
3. 配置以下信息：
   - **服务器地址**: WebSocket服务器地址（默认: ws://localhost:8080）
   - **代理站点**: 目标系统域名
   - **SSO地址**: 单点登录地址
   - **通知设置**: 桌面通知和声音提醒

#### 高级配置
在助手面板的"设置"Tab中可以进行更详细的配置。

## 功能使用说明

### 1. 辅助任务功能

#### 发起新任务
1. 在助手面板中选择"辅助任务"Tab
2. 点击"发起新任务"子Tab
3. 选择任务类型（数据导入、批量操作、报表生成等）
4. 下载对应的Excel模板
5. 填写模板后上传文件
6. 点击"提交任务"

#### 查看任务列表
1. 在"辅助任务"Tab中点击"任务列表"
2. 可以查看所有已提交的任务
3. 支持按任务名称、创建时间、状态筛选
4. 点击"查看详情"可以查看任务执行详情

### 2. 智能对话功能

#### 基本对话
1. 在助手面板中选择"智能对话"Tab
2. 在输入框中输入问题或指令
3. 点击"发送"或按回车键发送消息
4. 智能助手会实时回复

#### 快捷指令
- 点击预设的快捷指令按钮快速发送常用命令
- 支持"查看任务状态"、"帮助文档"等快捷操作

### 3. 状态监控

#### 连接状态
- 绿色：已连接到服务器
- 黄色：连接中
- 红色：连接失败或断开

#### 登录状态
- "已登录"：成功检测到目标系统登录状态
- "未获取到登录态"：未检测到登录信息，点击可跳转到SSO登录

## 故障排除

### 常见问题

#### 1. 插件图标不显示
**原因**: 当前网站不在目标系统列表中
**解决**: 
- 检查网站域名是否包含目标关键词
- 在设置中添加当前域名到代理站点列表

#### 2. 悬浮图标不出现
**原因**: Content Script未正确注入
**解决**:
- 刷新页面重试
- 检查Chrome控制台是否有错误信息
- 确认插件权限设置正确

#### 3. WebSocket连接失败
**原因**: 服务器地址配置错误或服务器未启动
**解决**:
- 检查设置中的服务器地址是否正确
- 确认服务器端程序正在运行
- 检查网络连接和防火墙设置

#### 4. 登录状态检测失败
**原因**: Cookie读取权限不足或目标系统Cookie格式特殊
**解决**:
- 确认插件有Cookie读取权限
- 检查目标系统是否正常登录
- 尝试重新登录目标系统

### 调试方法

#### 1. 查看控制台日志
1. 按F12打开开发者工具
2. 在Console标签中查看错误信息
3. 查找以"智能助手"或"APA"开头的日志

#### 2. 检查插件状态
1. 访问 `chrome://extensions/`
2. 找到"B端智能助手-APA"插件
3. 点击"详细信息"查看权限和错误信息
4. 点击"检查视图"可以调试background script

#### 3. 网络调试
1. 在开发者工具的Network标签中监控请求
2. 检查WebSocket连接状态
3. 查看HTTP请求是否正常发送

## 开发调试

### 本地开发
1. 修改代码后直接刷新扩展程序页面
2. 或者点击插件的"重新加载"按钮
3. 刷新测试页面查看效果

### 代码结构
- `manifest.json`: 插件配置
- `background.js`: 后台服务
- `content.js`: 页面注入脚本
- `content.css`: 样式文件
- `popup.html/js`: 弹出窗口

### 添加新功能
1. 在对应的JS文件中添加功能代码
2. 更新CSS样式（如需要）
3. 在manifest.json中声明新权限（如需要）
4. 重新加载插件测试

## 注意事项

1. **隐私安全**: 插件会读取网站Cookie，请确保在可信任的网站使用
2. **性能影响**: 插件会在页面中注入脚本，可能对页面性能有轻微影响
3. **兼容性**: 建议使用Chrome 88+版本，其他Chromium内核浏览器可能需要适配
4. **网络要求**: 需要能够访问配置的WebSocket服务器地址

## 技术支持

如遇到问题，请提供以下信息：
- Chrome浏览器版本
- 插件版本
- 错误信息截图
- 控制台日志
- 复现步骤

联系方式：[在此添加您的联系方式]
