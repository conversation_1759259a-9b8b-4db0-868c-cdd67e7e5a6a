<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>气泡吸附效果测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .demo-section {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .demo-section h2 {
            margin-top: 0;
            color: #fff;
            font-size: 1.5em;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            position: relative;
            padding-left: 30px;
        }

        .feature-list li:before {
            content: "✨";
            position: absolute;
            left: 0;
            top: 10px;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .instructions {
            background: rgba(76, 175, 80, 0.2);
            border-left: 4px solid #4CAF50;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .instructions h3 {
            margin-top: 0;
            color: #4CAF50;
        }

        .code-block {
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* 模拟悬浮气泡样式 */
        .demo-bubble {
            position: fixed;
            top: 30%;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            user-select: none;
            animation: breathing 3s ease-in-out infinite;
        }

        .demo-bubble:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(0,0,0,0.4);
            animation: none;
        }

        @keyframes breathing {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 6px 16px rgba(0,0,0,0.35);
            }
        }

        .demo-bubble.dragging {
            transform: scale(1.15);
            box-shadow: 0 8px 24px rgba(0,0,0,0.5);
            animation: none;
            cursor: grabbing;
            z-index: 1001;
        }

        .demo-bubble.edge-attached {
            opacity: 0.8;
        }

        .demo-bubble.edge-attached:hover {
            opacity: 1;
        }

        .notification-demo {
            animation: pulse-notification 2s infinite;
        }

        @keyframes pulse-notification {
            0% {
                transform: scale(1);
                box-shadow: 0 4px 12px rgba(0,0,0,0.3), 0 0 0 0 rgba(76, 175, 80, 0.7);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 6px 16px rgba(0,0,0,0.4), 0 0 0 10px rgba(76, 175, 80, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 4px 12px rgba(0,0,0,0.3), 0 0 0 0 rgba(76, 175, 80, 0);
            }
        }

        .control-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .control-panel button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
            transition: background 0.2s ease;
        }

        .control-panel button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 智能助手气泡吸附效果</h1>
        
        <div class="demo-section">
            <h2><span class="status-indicator"></span>功能特性</h2>
            <ul class="feature-list">
                <li><strong>磁性吸附</strong> - 拖拽释放后自动吸附到最近的屏幕边缘</li>
                <li><strong>呼吸动画</strong> - 待机时的微妙呼吸效果，增加生动感</li>
                <li><strong>拖拽交互</strong> - 支持鼠标和触摸拖拽，平滑跟随手势</li>
                <li><strong>智能定位</strong> - 窗口大小改变时自动调整位置</li>
                <li><strong>边缘半透明</strong> - 吸附到边缘时半透明，悬停时恢复</li>
                <li><strong>通知脉冲</strong> - 有新消息时的脉冲提醒效果</li>
                <li><strong>位置记忆</strong> - 记住用户上次拖拽的位置</li>
            </ul>
        </div>

        <div class="demo-section">
            <div class="instructions">
                <h3>🎯 使用说明</h3>
                <p>1. <strong>点击气泡</strong> - 打开/关闭智能助手面板</p>
                <p>2. <strong>拖拽气泡</strong> - 按住并拖动到任意位置</p>
                <p>3. <strong>释放气泡</strong> - 自动吸附到最近的屏幕边缘</p>
                <p>4. <strong>调整窗口</strong> - 改变浏览器窗口大小，气泡会自动调整</p>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔧 技术实现</h2>
            <div class="code-block">
// 核心吸附算法
magneticSnap(icon) {
  const rect = icon.getBoundingClientRect();
  const centerX = rect.left + rect.width / 2;
  const centerY = rect.top + rect.height / 2;
  
  // 计算到各边的距离
  const distances = {
    left: centerX,
    right: window.innerWidth - centerX,
    top: centerY,
    bottom: window.innerHeight - centerY
  };
  
  // 吸附到最近的边
  const minDistance = Math.min(...Object.values(distances));
  // ... 执行动画吸附
}
            </div>
        </div>
    </div>

    <!-- 演示气泡 -->
    <div class="demo-bubble" id="demoBubble">🤖</div>

    <!-- 控制面板 -->
    <div class="control-panel">
        <div style="color: white; margin-bottom: 10px; font-size: 14px;">演示控制</div>
        <button onclick="triggerNotification()">触发通知</button>
        <button onclick="resetPosition()">重置位置</button>
        <button onclick="toggleEdgeMode()">切换边缘模式</button>
    </div>

    <script>
        // 演示气泡拖拽功能
        const demoBubble = document.getElementById('demoBubble');
        let isDragging = false;
        let startX, startY, startLeft, startTop;
        let clickStartTime = 0;
        let hasMoved = false;

        // 鼠标事件
        demoBubble.addEventListener('mousedown', (e) => {
            isDragging = true;
            hasMoved = false;
            clickStartTime = Date.now();
            
            startX = e.clientX;
            startY = e.clientY;
            
            const rect = demoBubble.getBoundingClientRect();
            startLeft = rect.left;
            startTop = rect.top;
            
            demoBubble.classList.add('dragging');
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            if (Math.abs(deltaX) > 5 || Math.abs(deltaY) > 5) {
                hasMoved = true;
            }
            
            const newLeft = startLeft + deltaX;
            const newTop = startTop + deltaY;
            
            const maxLeft = window.innerWidth - demoBubble.offsetWidth;
            const maxTop = window.innerHeight - demoBubble.offsetHeight;
            
            demoBubble.style.left = Math.max(0, Math.min(newLeft, maxLeft)) + 'px';
            demoBubble.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
            demoBubble.style.right = 'auto';
            
            e.preventDefault();
        });

        document.addEventListener('mouseup', () => {
            if (!isDragging) return;
            
            isDragging = false;
            demoBubble.classList.remove('dragging');
            
            const clickDuration = Date.now() - clickStartTime;
            if (!hasMoved && clickDuration < 200) {
                alert('🎉 气泡被点击了！\n在真实插件中，这里会打开智能助手面板。');
                return;
            }
            
            if (hasMoved) {
                magneticSnap();
            }
        });

        // 磁性吸附功能
        function magneticSnap() {
            const rect = demoBubble.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            
            const distanceToLeft = centerX;
            const distanceToRight = windowWidth - centerX;
            const distanceToTop = centerY;
            const distanceToBottom = windowHeight - centerY;
            
            const minDistance = Math.min(distanceToLeft, distanceToRight, distanceToTop, distanceToBottom);
            
            let targetLeft, targetTop;
            let isEdgeAttached = false;
            
            if (minDistance === distanceToLeft || minDistance === distanceToRight) {
                if (distanceToLeft < distanceToRight) {
                    targetLeft = 20;
                    isEdgeAttached = true;
                } else {
                    targetLeft = windowWidth - demoBubble.offsetWidth - 20;
                    isEdgeAttached = true;
                }
                targetTop = Math.max(20, Math.min(rect.top, windowHeight - demoBubble.offsetHeight - 20));
            } else {
                if (distanceToTop < distanceToBottom) {
                    targetTop = 20;
                } else {
                    targetTop = windowHeight - demoBubble.offsetHeight - 20;
                }
                targetLeft = Math.max(20, Math.min(rect.left, windowWidth - demoBubble.offsetWidth - 20));
            }
            
            demoBubble.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            demoBubble.style.left = targetLeft + 'px';
            demoBubble.style.top = targetTop + 'px';
            
            if (isEdgeAttached) {
                setTimeout(() => {
                    demoBubble.classList.add('edge-attached');
                }, 300);
            } else {
                demoBubble.classList.remove('edge-attached');
            }
            
            setTimeout(() => {
                demoBubble.style.transition = '';
            }, 300);
        }

        // 控制面板功能
        function triggerNotification() {
            demoBubble.classList.add('notification-demo');
            setTimeout(() => {
                demoBubble.classList.remove('notification-demo');
            }, 3000);
        }

        function resetPosition() {
            demoBubble.style.transition = 'all 0.3s ease';
            demoBubble.style.right = '30px';
            demoBubble.style.top = '30%';
            demoBubble.style.left = 'auto';
            demoBubble.classList.remove('edge-attached');
            
            setTimeout(() => {
                demoBubble.style.transition = '';
            }, 300);
        }

        function toggleEdgeMode() {
            demoBubble.classList.toggle('edge-attached');
        }

        // 窗口大小改变处理
        window.addEventListener('resize', () => {
            const rect = demoBubble.getBoundingClientRect();
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;

            let needsReposition = false;
            let newLeft = rect.left;
            let newTop = rect.top;

            if (rect.right > windowWidth) {
                newLeft = windowWidth - demoBubble.offsetWidth - 20;
                needsReposition = true;
            }
            if (rect.bottom > windowHeight) {
                newTop = windowHeight - demoBubble.offsetHeight - 20;
                needsReposition = true;
            }
            if (rect.left < 0) {
                newLeft = 20;
                needsReposition = true;
            }
            if (rect.top < 0) {
                newTop = 20;
                needsReposition = true;
            }

            if (needsReposition) {
                demoBubble.style.transition = 'all 0.3s ease';
                demoBubble.style.left = newLeft + 'px';
                demoBubble.style.top = newTop + 'px';
                demoBubble.style.right = 'auto';

                setTimeout(() => {
                    demoBubble.style.transition = '';
                }, 300);
            }
        });
    </script>
</body>
</html>
