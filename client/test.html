<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面 - ERP系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            margin: -20px -20px 20px -20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .nav {
            background: #34495e;
            padding: 10px 0;
            margin: -20px -20px 20px -20px;
        }
        .nav ul {
            list-style: none;
            margin: 0;
            padding: 0 20px;
            display: flex;
        }
        .nav li {
            margin-right: 30px;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            display: block;
            border-radius: 4px;
        }
        .nav a:hover {
            background: #2c3e50;
        }
        .content {
            padding: 20px 0;
        }
        .card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .status {
            padding: 5px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.online {
            background: #d4edda;
            color: #155724;
        }
        .status.offline {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>企业ERP管理系统</h1>
        <p>欢迎使用智能化企业资源规划系统</p>
    </div>

    <div class="container">
        <div class="nav">
            <ul>
                <li><a href="#dashboard">仪表盘</a></li>
                <li><a href="#orders">订单管理</a></li>
                <li><a href="#inventory">库存管理</a></li>
                <li><a href="#finance">财务管理</a></li>
                <li><a href="#reports">报表中心</a></li>
                <li><a href="#settings">系统设置</a></li>
            </ul>
        </div>

        <div class="content">
            <div class="card">
                <h3>系统状态</h3>
                <p>当前登录用户: <strong>张华 (<EMAIL>)</strong></p>
                <p>系统状态: <span class="status online">在线</span></p>
                <p>最后登录时间: 2024-01-15 14:30:25</p>
            </div>

            <div class="card">
                <h3>快速操作</h3>
                <button class="btn" onclick="createOrder()">创建订单</button>
                <button class="btn" onclick="checkInventory()">查看库存</button>
                <button class="btn" onclick="generateReport()">生成报表</button>
                <button class="btn" onclick="exportData()">导出数据</button>
            </div>

            <div class="card">
                <h3>数据导入</h3>
                <div class="form-group">
                    <label>选择导入类型:</label>
                    <select id="importType">
                        <option value="">请选择...</option>
                        <option value="orders">订单数据</option>
                        <option value="products">产品数据</option>
                        <option value="customers">客户数据</option>
                        <option value="suppliers">供应商数据</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>选择文件:</label>
                    <input type="file" id="importFile" accept=".xlsx,.xls,.csv">
                </div>
                <button class="btn" onclick="importData()">开始导入</button>
            </div>

            <div class="card">
                <h3>最近任务</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">任务ID</th>
                            <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">任务名称</th>
                            <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">状态</th>
                            <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="padding: 10px; border: 1px solid #ddd;">T001</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">订单数据导入</td>
                            <td style="padding: 10px; border: 1px solid #ddd;"><span class="status online">完成</span></td>
                            <td style="padding: 10px; border: 1px solid #ddd;">2024-01-15 10:30</td>
                        </tr>
                        <tr>
                            <td style="padding: 10px; border: 1px solid #ddd;">T002</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">库存盘点</td>
                            <td style="padding: 10px; border: 1px solid #ddd;"><span class="status offline">进行中</span></td>
                            <td style="padding: 10px; border: 1px solid #ddd;">2024-01-15 11:15</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 模拟一些Cookie设置，用于测试登录状态检测
        document.cookie = "session_id=abc123def456; path=/; domain=" + window.location.hostname;
        document.cookie = "auth_token=xyz789uvw012; path=/; domain=" + window.location.hostname;
        document.cookie = "user_id=12345; path=/; domain=" + window.location.hostname;

        function createOrder() {
            alert('创建订单功能 - 这里可以被智能助手自动化');
        }

        function checkInventory() {
            alert('查看库存功能 - 这里可以被智能助手自动化');
        }

        function generateReport() {
            alert('生成报表功能 - 这里可以被智能助手自动化');
        }

        function exportData() {
            alert('导出数据功能 - 这里可以被智能助手自动化');
        }

        function importData() {
            const type = document.getElementById('importType').value;
            const file = document.getElementById('importFile').files[0];
            
            if (!type) {
                alert('请选择导入类型');
                return;
            }
            
            if (!file) {
                alert('请选择文件');
                return;
            }
            
            alert(`开始导入 ${type} 数据，文件: ${file.name}`);
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            console.log('ERP系统页面加载完成，智能助手应该可以检测到这是一个目标系统');
        });
    </script>
</body>
</html>
