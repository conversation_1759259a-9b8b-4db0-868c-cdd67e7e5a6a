<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>B端智能助手-APA</title>
  <style>
    body {
      width: 300px;
      padding: 16px;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      background: #f8f9fa;
    }
    
    .header {
      text-align: center;
      margin-bottom: 16px;
    }
    
    .logo {
      font-size: 32px;
      margin-bottom: 8px;
    }
    
    .title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 4px;
    }
    
    .subtitle {
      font-size: 12px;
      color: #666;
    }
    
    .status-section {
      background: white;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 16px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
    
    .status-item:last-child {
      margin-bottom: 0;
    }
    
    .status-label {
      font-size: 13px;
      color: #333;
    }
    
    .status-value {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 12px;
      background: #e9ecef;
      color: #666;
    }
    
    .status-value.connected {
      background: #d4edda;
      color: #155724;
    }
    
    .status-value.disconnected {
      background: #f8d7da;
      color: #721c24;
    }
    
    .status-value.logged-in {
      background: #d4edda;
      color: #155724;
    }
    
    .status-value.not-logged-in {
      background: #fff3cd;
      color: #856404;
    }
    
    .actions {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    .btn {
      padding: 10px 16px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 13px;
      font-weight: 500;
      transition: all 0.2s ease;
    }
    
    .btn-primary {
      background: #4CAF50;
      color: white;
    }
    
    .btn-primary:hover {
      background: #45a049;
    }
    
    .btn-secondary {
      background: white;
      color: #333;
      border: 1px solid #ddd;
    }
    
    .btn-secondary:hover {
      background: #f8f9fa;
    }
    
    .footer {
      text-align: center;
      margin-top: 16px;
      font-size: 11px;
      color: #999;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo">🤖</div>
    <div class="title">B端智能助手</div>
    <div class="subtitle">APA - Automated Process Assistant</div>
  </div>
  
  <div class="status-section">
    <div class="status-item">
      <span class="status-label">连接状态</span>
      <span class="status-value disconnected" id="connection-status">未连接</span>
    </div>
    <div class="status-item">
      <span class="status-label">登录状态</span>
      <span class="status-value not-logged-in" id="login-status">检查中...</span>
    </div>
    <div class="status-item">
      <span class="status-label">当前站点</span>
      <span class="status-value" id="current-site">-</span>
    </div>
  </div>
  
  <div class="actions">
    <button class="btn btn-primary" id="open-assistant">打开助手面板</button>
    <button class="btn btn-secondary" id="open-settings">设置</button>
    <button class="btn btn-secondary" id="refresh-status">刷新状态</button>
  </div>
  
  <div class="footer">
    版本 1.0.0 | 智能RPA助手
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
