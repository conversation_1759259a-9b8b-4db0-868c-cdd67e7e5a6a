<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能助手气泡菜单 - 增强演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }

        .demo-header {
            text-align: center;
            padding: 40px 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 16px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .demo-header h1 {
            font-size: 2.5em;
            margin: 0 0 10px 0;
            background: linear-gradient(45deg, #4CAF50, #81C784);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .demo-header p {
            font-size: 1.2em;
            opacity: 0.9;
            margin: 0;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 24px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card h3 {
            margin: 0 0 12px 0;
            color: #4CAF50;
            font-size: 1.3em;
        }

        .feature-card p {
            margin: 0;
            opacity: 0.9;
            line-height: 1.6;
        }

        .demo-controls {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }

        .demo-controls h4 {
            margin: 0 0 15px 0;
            color: #4CAF50;
        }

        .control-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 13px;
            transition: all 0.2s ease;
        }

        .control-button:hover {
            background: #45a049;
            transform: translateY(-1px);
        }

        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            border-radius: 12px;
            padding: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }

        .status-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .content-area {
            background: rgba(255,255,255,0.05);
            border-radius: 12px;
            padding: 30px;
            margin: 20px 0;
            backdrop-filter: blur(5px);
        }

        .highlight-box {
            background: linear-gradient(45deg, rgba(76, 175, 80, 0.2), rgba(129, 199, 132, 0.2));
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .code-snippet {
            background: rgba(0,0,0,0.4);
            border-radius: 8px;
            padding: 16px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
            border: 1px solid rgba(255,255,255,0.1);
        }

        /* 模拟管理系统界面元素 */
        .admin-nav {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .nav-item {
            background: rgba(76, 175, 80, 0.2);
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            border: 1px solid rgba(76, 175, 80, 0.3);
        }

        @media (max-width: 768px) {
            .demo-controls {
                position: relative;
                bottom: auto;
                left: auto;
                margin: 20px 0;
            }

            .status-indicator {
                position: relative;
                top: auto;
                right: auto;
                margin: 20px 0;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-header">
        <h1>🤖 智能助手气泡菜单</h1>
        <p>增强版演示 - 智能定位 · 流畅动画 · 响应式设计</p>
    </div>

    <div class="status-indicator">
        <div class="status-item">
            <div class="status-dot"></div>
            <span>智能助手已激活</span>
        </div>
        <div class="status-item">
            <div class="status-dot"></div>
            <span>目标系统已检测</span>
        </div>
        <div class="status-item">
            <div class="status-dot"></div>
            <span>气泡菜单就绪</span>
        </div>
    </div>

    <div class="features-grid">
        <div class="feature-card">
            <h3>🎯 智能定位</h3>
            <p>气泡菜单会根据当前位置智能调整助手面板的显示位置，确保最佳的用户体验。支持左右、上下自适应定位。</p>
        </div>

        <div class="feature-card">
            <h3>✨ 流畅动画</h3>
            <p>采用现代CSS动画和JavaScript协调，实现丝滑的入场、退场动画效果。支持硬件加速，性能优异。</p>
        </div>

        <div class="feature-card">
            <h3>🔍 智能检测</h3>
            <p>自动识别B端管理系统，支持多种域名模式和页面特征检测。开发环境友好，支持localhost测试。</p>
        </div>

        <div class="feature-card">
            <h3>📱 响应式设计</h3>
            <p>完美适配桌面端和移动端，自动调整布局和交互方式。支持触摸操作和鼠标操作。</p>
        </div>

        <div class="feature-card">
            <h3>🎨 现代化UI</h3>
            <p>采用毛玻璃效果、渐变色彩和微妙阴影，打造现代化的视觉体验。支持深色和浅色主题。</p>
        </div>

        <div class="feature-card">
            <h3>⚡ 高性能</h3>
            <p>优化的事件处理和动画性能，最小化对页面性能的影响。智能的资源管理和内存优化。</p>
        </div>
    </div>

    <!-- 模拟管理系统导航 -->
    <div class="content-area">
        <h2>模拟管理系统界面</h2>
        <div class="admin-nav">
            <div class="nav-item">系统管理</div>
            <div class="nav-item">用户管理</div>
            <div class="nav-item">数据管理</div>
            <div class="nav-item">报表中心</div>
            <div class="nav-item">设置中心</div>
        </div>
        
        <div class="highlight-box">
            <h3>🎯 使用说明</h3>
            <p><strong>点击气泡</strong> - 打开智能助手面板，面板会根据气泡位置智能定位</p>
            <p><strong>拖拽气泡</strong> - 拖动到任意位置，释放后自动吸附到最近边缘</p>
            <p><strong>点击外部</strong> - 点击面板外部区域可关闭助手面板</p>
            <p><strong>响应式</strong> - 调整浏览器窗口大小测试响应式效果</p>
        </div>

        <div class="code-snippet">
// 核心特性代码示例
class EnhancedAssistant {
  // 智能定位算法
  positionAssistantPanel() {
    const iconRect = this.icon.getBoundingClientRect();
    const panelWidth = 420, panelHeight = 640;
    
    // 智能选择最佳显示位置
    const position = this.calculateOptimalPosition(iconRect);
    this.applyPosition(position);
  }
  
  // 流畅动画效果
  showWithAnimation() {
    this.container.style.display = 'block';
    requestAnimationFrame(() => {
      this.container.classList.add('apa-show');
    });
  }
}
        </div>
    </div>

    <div class="demo-controls">
        <h4>🎮 演示控制</h4>
        <button class="control-button" onclick="triggerNotification()">触发通知效果</button>
        <button class="control-button" onclick="resetBubblePosition()">重置气泡位置</button>
        <button class="control-button" onclick="toggleEdgeMode()">切换边缘模式</button>
        <button class="control-button" onclick="simulateResize()">模拟窗口调整</button>
    </div>

    <script>
        // 演示控制函数
        function triggerNotification() {
            console.log('🔔 触发通知效果');
            // 这里会触发气泡的通知动画
        }

        function resetBubblePosition() {
            console.log('🔄 重置气泡位置');
            // 重置到默认位置
        }

        function toggleEdgeMode() {
            console.log('🔄 切换边缘模式');
            // 切换边缘吸附效果
        }

        function simulateResize() {
            console.log('📐 模拟窗口调整');
            // 模拟窗口大小变化
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 增强版气泡菜单演示页面已加载');
            console.log('💡 请安装Chrome插件以体验完整功能');
        });
    </script>
</body>
</html>
