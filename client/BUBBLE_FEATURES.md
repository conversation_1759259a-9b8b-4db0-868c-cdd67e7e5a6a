# 🫧 智能助手气泡吸附效果

## ✨ 功能特性

### 1. 磁性吸附 (Magnetic Snap)
- **自动吸附**: 拖拽释放后自动吸附到最近的屏幕边缘
- **智能算法**: 计算到各边的距离，选择最优吸附位置
- **优先级**: 优先吸附到左右两侧，保持更好的用户体验

### 2. 呼吸动画 (Breathing Animation)
- **微妙动画**: 待机时的轻柔呼吸效果，增加生动感
- **智能暂停**: 悬停或拖拽时自动停止动画
- **性能优化**: 使用CSS动画，GPU加速

### 3. 拖拽交互 (Drag Interaction)
- **平滑跟随**: 支持鼠标和触摸拖拽，流畅跟随手势
- **边界限制**: 自动限制在视窗范围内
- **点击检测**: 智能区分点击和拖拽操作

### 4. 智能定位 (Smart Positioning)
- **窗口适应**: 浏览器窗口大小改变时自动调整位置
- **位置记忆**: 记住用户上次拖拽的位置
- **边缘检测**: 防止图标超出可视范围

### 5. 视觉反馈 (Visual Feedback)
- **边缘半透明**: 吸附到边缘时半透明，悬停时恢复
- **拖拽状态**: 拖拽时放大显示，增强视觉反馈
- **通知脉冲**: 有新消息时的脉冲提醒效果

## 🎯 使用方法

### 基本操作
1. **点击气泡** - 打开/关闭智能助手面板
2. **拖拽气泡** - 按住并拖动到任意位置
3. **释放气泡** - 自动吸附到最近的屏幕边缘
4. **调整窗口** - 改变浏览器窗口大小，气泡会自动调整

### 高级功能
- **通知提醒**: 收到新消息时会显示脉冲动画
- **位置记忆**: 下次访问时会恢复到上次的位置
- **边缘模式**: 吸附到边缘时会变为半透明状态

## 🔧 技术实现

### 核心算法
```javascript
// 磁性吸附算法
magneticSnap(icon) {
  const rect = icon.getBoundingClientRect();
  const centerX = rect.left + rect.width / 2;
  const centerY = rect.top + rect.height / 2;
  
  // 计算到各边的距离
  const distances = {
    left: centerX,
    right: window.innerWidth - centerX,
    top: centerY,
    bottom: window.innerHeight - centerY
  };
  
  // 找到最近的边并执行吸附
  const minDistance = Math.min(...Object.values(distances));
  // ... 执行动画吸附
}
```

### 动画效果
```css
/* 呼吸动画 */
@keyframes apa-breathing {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0,0,0,0.35);
  }
}

/* 脉冲通知 */
@keyframes apa-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3), 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0,0,0,0.4), 0 0 0 10px rgba(76, 175, 80, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3), 0 0 0 0 rgba(76, 175, 80, 0);
  }
}
```

## 📱 兼容性

### 浏览器支持
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

### 设备支持
- ✅ 桌面端 (鼠标操作)
- ✅ 移动端 (触摸操作)
- ✅ 平板端 (触摸操作)

### 响应式设计
- 自动适应不同屏幕尺寸
- 移动端优化的触摸体验
- 高DPI屏幕支持

## 🎨 自定义配置

### CSS变量
```css
:root {
  --bubble-size: 60px;
  --bubble-color: #4CAF50;
  --bubble-shadow: 0 4px 12px rgba(0,0,0,0.3);
  --animation-duration: 3s;
  --snap-duration: 0.3s;
}
```

### JavaScript配置
```javascript
// 自定义配置选项
const config = {
  snapDistance: 20,        // 吸附距离
  dragThreshold: 5,        // 拖拽阈值
  animationDuration: 300,  // 动画时长
  breathingEnabled: true,  // 呼吸动画
  edgeOpacity: 0.8        // 边缘透明度
};
```

## 🚀 演示页面

打开 `bubble-test.html` 可以体验完整的气泡吸附效果：

```bash
# 在浏览器中打开演示页面
open client/bubble-test.html
```

演示页面包含：
- 完整的拖拽和吸附功能
- 实时的视觉反馈效果
- 控制面板用于测试各种功能
- 详细的功能说明和使用指南

## 📝 更新日志

### v2.0.0 - 气泡吸附效果
- ✨ 新增磁性吸附功能
- ✨ 新增呼吸动画效果
- ✨ 新增拖拽交互支持
- ✨ 新增智能定位算法
- ✨ 新增边缘半透明效果
- ✨ 新增通知脉冲动画
- ✨ 新增位置记忆功能
- 🐛 修复窗口大小改变时的定位问题
- 🎨 优化动画性能和视觉效果
- 📱 增强移动端触摸体验

### v1.0.0 - 基础版本
- ✅ 基本的悬浮图标功能
- ✅ 简单的点击交互
- ✅ 固定位置显示
