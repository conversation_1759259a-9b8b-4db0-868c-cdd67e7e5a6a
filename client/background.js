// B端智能助手-APA 背景脚本
class AssistantBackground {
  constructor() {
    this.websocket = null;
    this.serverUrl = '';
    this.connectionStatus = 'disconnected';
    this.init();
  }

  init() {
    // 监听插件安装
    chrome.runtime.onInstalled.addListener(() => {
      console.log('智能助手插件已安装');
      this.initDefaultSettings();
    });

    // 监听来自content script的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // 保持消息通道开放
    });

    // 监听标签页更新
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && tab.url) {
        this.checkTargetSystem(tab);
      }
    });
  }

  // 初始化默认设置
  async initDefaultSettings() {
    const defaultSettings = {
      serverUrl: 'ws://localhost:8080',
      targetSites: [],
      ssoUrls: {},
      desktopNotification: true,
      soundAlert: true
    };

    await chrome.storage.local.set({ settings: defaultSettings });
  }

  // 处理消息
  async handleMessage(request, sender, sendResponse) {
    switch (request.action) {
      case 'getLoginStatus':
        const loginStatus = await this.checkLoginStatus(sender.tab);
        sendResponse({ status: loginStatus });
        break;

      case 'getCookies':
        const cookies = await this.getCookies(sender.tab.url);
        sendResponse({ cookies });
        break;

      case 'connectWebSocket':
        await this.connectWebSocket();
        sendResponse({ status: this.connectionStatus });
        break;

      case 'sendMessage':
        this.sendWebSocketMessage(request.data);
        sendResponse({ success: true });
        break;

      case 'submitTask':
        const result = await this.submitTask(request.taskData);
        sendResponse(result);
        break;

      case 'getConnectionStatus':
        sendResponse({ status: this.connectionStatus });
        break;

      case 'openAssistant':
        // 转发给content script
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        chrome.tabs.sendMessage(tab.id, { action: 'showAssistant' });
        sendResponse({ success: true });
        break;

      default:
        sendResponse({ error: 'Unknown action' });
    }
  }

  // 检查目标系统
  async checkTargetSystem(tab) {
    const settings = await chrome.storage.local.get('settings');
    const targetSites = settings.settings?.targetSites || [];

    const isTargetSite = targetSites.some(site =>
      tab.url.includes(site)
    );

    if (isTargetSite) {
      // 注入悬浮图标
      chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: this.injectFloatingIcon
      });
    }
  }

  // 注入悬浮图标的函数
  injectFloatingIcon() {
    if (document.getElementById('apa-floating-icon')) return;

    const floatingIcon = document.createElement('div');
    floatingIcon.id = 'apa-floating-icon';
    floatingIcon.innerHTML = '🤖';
    floatingIcon.style.cssText = `
      position: fixed;
      top: 50%;
      right: 20px;
      width: 60px;
      height: 60px;
      background: #4CAF50;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      cursor: pointer;
      z-index: 10000;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      transition: all 0.3s ease;
    `;

    floatingIcon.addEventListener('click', () => {
      window.postMessage({ type: 'TOGGLE_ASSISTANT' }, '*');
    });

    document.body.appendChild(floatingIcon);
  }

  // 检查登录状态
  async checkLoginStatus(tab) {
    try {
      const cookies = await chrome.cookies.getAll({ url: tab.url });
      const loginCookies = cookies.filter(cookie =>
        cookie.name.toLowerCase().includes('session') ||
        cookie.name.toLowerCase().includes('token') ||
        cookie.name.toLowerCase().includes('auth')
      );

      return loginCookies.length > 0 ? 'logged_in' : 'not_logged_in';
    } catch (error) {
      console.error('检查登录状态失败:', error);
      return 'error';
    }
  }

  // 获取Cookies
  async getCookies(url) {
    try {
      const cookies = await chrome.cookies.getAll({ url });
      return cookies;
    } catch (error) {
      console.error('获取Cookies失败:', error);
      return [];
    }
  }

  // 连接WebSocket
  async connectWebSocket() {
    const settings = await chrome.storage.local.get('settings');
    this.serverUrl = settings.settings?.serverUrl || 'ws://localhost:8080';

    try {
      this.connectionStatus = 'connecting';
      this.websocket = new WebSocket(this.serverUrl);

      this.websocket.onopen = () => {
        this.connectionStatus = 'connected';
        console.log('WebSocket连接成功');
      };

      this.websocket.onmessage = (event) => {
        this.handleWebSocketMessage(JSON.parse(event.data));
      };

      this.websocket.onclose = () => {
        this.connectionStatus = 'disconnected';
        console.log('WebSocket连接关闭');
        // 自动重连
        setTimeout(() => this.connectWebSocket(), 5000);
      };

      this.websocket.onerror = (error) => {
        this.connectionStatus = 'error';
        console.error('WebSocket错误:', error);
      };

    } catch (error) {
      this.connectionStatus = 'error';
      console.error('WebSocket连接失败:', error);
    }
  }

  // 处理WebSocket消息
  handleWebSocketMessage(data) {
    // 广播消息给所有content scripts
    chrome.tabs.query({}, (tabs) => {
      tabs.forEach(tab => {
        chrome.tabs.sendMessage(tab.id, {
          action: 'websocketMessage',
          data: data
        }).catch(() => {}); // 忽略错误
      });
    });
  }

  // 发送WebSocket消息
  sendWebSocketMessage(data) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(data));
    } else {
      console.error('WebSocket未连接');
    }
  }

  // 提交任务
  async submitTask(taskData) {
    const settings = await chrome.storage.local.get('settings');
    const serverUrl = settings.settings?.serverUrl?.replace('ws://', 'http://').replace('wss://', 'https://');

    try {
      const response = await fetch(`${serverUrl}/api/tasks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(taskData)
      });

      return await response.json();
    } catch (error) {
      console.error('提交任务失败:', error);
      return { success: false, error: error.message };
    }
  }
}

// 初始化背景脚本
new AssistantBackground();
