// B端智能助手-APA Popup脚本
class AssistantPopup {
  constructor() {
    this.init();
  }

  init() {
    this.bindEvents();
    this.updateStatus();
  }

  bindEvents() {
    // 打开助手面板
    document.getElementById('open-assistant').addEventListener('click', () => {
      this.openAssistant();
    });

    // 打开设置
    document.getElementById('open-settings').addEventListener('click', () => {
      this.openSettings();
    });

    // 刷新状态
    document.getElementById('refresh-status').addEventListener('click', () => {
      this.updateStatus();
    });
  }

  // 打开助手面板
  async openAssistant() {
    try {
      // 获取当前活动标签页
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      // 向content script发送消息打开助手
      await chrome.tabs.sendMessage(tab.id, { action: 'openAssistant' });
      
      // 关闭popup
      window.close();
    } catch (error) {
      console.error('打开助手失败:', error);
      
      // 如果content script未加载，尝试注入
      try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          files: ['content.js']
        });
        
        // 重试打开助手
        setTimeout(() => {
          chrome.tabs.sendMessage(tab.id, { action: 'openAssistant' });
          window.close();
        }, 500);
      } catch (injectError) {
        alert('请在支持的网站页面中使用助手');
      }
    }
  }

  // 打开设置
  openSettings() {
    // 可以打开一个新的设置页面，或者在助手面板中打开设置tab
    this.openAssistant();
  }

  // 更新状态
  async updateStatus() {
    try {
      // 获取当前标签页
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      // 更新当前站点
      const hostname = new URL(tab.url).hostname;
      document.getElementById('current-site').textContent = hostname;
      
      // 检查登录状态
      const loginResponse = await chrome.runtime.sendMessage({ action: 'getLoginStatus' });
      this.updateLoginStatus(loginResponse.status);
      
      // 检查连接状态
      this.updateConnectionStatus();
      
    } catch (error) {
      console.error('更新状态失败:', error);
      document.getElementById('current-site').textContent = '无法获取';
      document.getElementById('login-status').textContent = '检查失败';
    }
  }

  // 更新登录状态
  updateLoginStatus(status) {
    const statusElement = document.getElementById('login-status');
    
    switch (status) {
      case 'logged_in':
        statusElement.textContent = '已登录';
        statusElement.className = 'status-value logged-in';
        break;
      case 'not_logged_in':
        statusElement.textContent = '未登录';
        statusElement.className = 'status-value not-logged-in';
        break;
      default:
        statusElement.textContent = '未知';
        statusElement.className = 'status-value';
    }
  }

  // 更新连接状态
  updateConnectionStatus() {
    const statusElement = document.getElementById('connection-status');
    
    // 这里可以通过background script获取实际的连接状态
    chrome.runtime.sendMessage({ action: 'getConnectionStatus' }, (response) => {
      if (response && response.status) {
        switch (response.status) {
          case 'connected':
            statusElement.textContent = '已连接';
            statusElement.className = 'status-value connected';
            break;
          case 'connecting':
            statusElement.textContent = '连接中';
            statusElement.className = 'status-value';
            break;
          case 'disconnected':
            statusElement.textContent = '未连接';
            statusElement.className = 'status-value disconnected';
            break;
          default:
            statusElement.textContent = '未知';
            statusElement.className = 'status-value';
        }
      }
    });
  }
}

// 初始化popup
document.addEventListener('DOMContentLoaded', () => {
  new AssistantPopup();
});
