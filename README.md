# B端智能助手-APA (Automated Process Assistant)

## 项目概述

B端智能助手-APA是一个完整的RPA（机器人流程自动化）解决方案，包含Chrome浏览器插件客户端和服务端自动化引擎。主要解决企业遗留系统API开放度低、系统改造成本高等问题。

## 项目架构

```
BAssitant/
├── client/                 # 🖥️ 客户端插件 (已完成)
│   ├── manifest.json       # Chrome插件配置
│   ├── background.js       # 后台服务脚本
│   ├── content.js          # 内容注入脚本
│   ├── content.css         # 界面样式
│   ├── popup.html/js       # 弹出窗口
│   ├── icons/              # 插件图标
│   ├── assets/             # 静态资源
│   └── test.html           # 测试页面
├── server/                 # 🚀 服务端引擎 (待开发)
│   ├── src/                # 源代码目录
│   ├── config/             # 配置文件
│   ├── scripts/            # 部署脚本
│   └── tests/              # 测试代码
├── docs/                   # 📚 项目文档
│   ├── Doc/                # 原始设计文档
│   ├── README.md           # 详细说明
│   ├── INSTALL.md          # 安装指南
│   ├── DEPLOYMENT.md       # 部署指南
│   └── DEMO.md             # 演示说明
├── package.json            # 项目配置
└── generate_icons.py       # 工具脚本
```

## 🎯 功能特性

### 客户端插件 (✅ 已完成)
- **悬浮助手**: 智能检测目标系统，自动显示助手图标
- **任务管理**: 支持Excel模板导入，批量任务处理
- **智能对话**: 自然语言交互，快捷指令支持
- **登录检测**: 自动识别系统登录状态，Cookie采集
- **实时通信**: WebSocket双向通信，状态实时更新

### 服务端引擎 (🔄 规划中)
- **RPA执行**: 浏览器自动化，模拟用户操作
- **任务调度**: 队列管理，并发控制，错误重试
- **数据处理**: 文件解析，数据转换，结果输出
- **API服务**: RESTful接口，WebSocket服务
- **监控报表**: 执行统计，性能分析，日志管理

## 🚀 快速开始

### 客户端插件安装

1. **安装Chrome插件**
   ```bash
   # 1. 打开 chrome://extensions/
   # 2. 开启"开发者模式"
   # 3. 点击"加载已解压的扩展程序"
   # 4. 选择 client/ 目录
   ```

2. **测试功能**
   ```bash
   # 在浏览器中打开测试页面
   open client/test.html
   ```

3. **查看文档**
   ```bash
   # 详细安装说明
   open docs/INSTALL.md
   
   # 演示指南
   open docs/DEMO.md
   ```

### 服务端开发 (即将开始)

```bash
# 进入服务端目录
cd server/

# 安装依赖 (Java + Spring Boot)
mvn install

# 启动服务
mvn spring-boot:run
```

## 📋 开发状态

| 模块 | 状态 | 进度 | 说明 |
|------|------|------|------|
| 客户端插件 | ✅ 完成 | 100% | 所有功能已实现，可立即使用 |
| WebSocket服务 | 🔄 规划 | 0% | 实时通信服务器 |
| RPA执行引擎 | 🔄 规划 | 0% | 浏览器自动化核心 |
| 任务调度器 | 🔄 规划 | 0% | 队列管理和任务分发 |
| API接口 | 🔄 规划 | 0% | RESTful服务接口 |
| 监控系统 | 🔄 规划 | 0% | 日志、统计、报表 |

## 🛠️ 技术栈

### 客户端
- **Chrome Extension API**: 浏览器扩展开发
- **JavaScript ES6+**: 现代前端技术
- **CSS3**: 响应式界面设计
- **WebSocket**: 实时双向通信

### 服务端 (规划)
- **Java + Spring Boot**: 主要后端框架
- **Redis**: 缓存和任务队列
- **Selenium**: 浏览器自动化
- **MySQL/PostgreSQL**: 数据持久化
- **Docker**: 容器化部署

## 📖 文档导航

- **[项目详细说明](docs/README.md)** - 完整的功能介绍
- **[安装使用指南](docs/INSTALL.md)** - 详细的安装步骤
- **[部署运维指南](docs/DEPLOYMENT.md)** - 生产环境部署
- **[演示操作指南](docs/DEMO.md)** - 功能演示说明
- **[原始设计文档](docs/Doc/)** - PDF设计文档

## 🎯 下一步计划

### 第一阶段：服务端基础架构
1. **项目初始化**
   - Spring Boot项目搭建
   - 基础配置和依赖管理
   - 数据库设计和初始化

2. **WebSocket服务**
   - 实时通信服务器
   - 消息路由和广播
   - 客户端连接管理

3. **基础API**
   - 用户认证接口
   - 任务CRUD接口
   - 文件上传处理

### 第二阶段：RPA核心功能
1. **浏览器自动化**
   - Selenium集成
   - 操作录制和回放
   - 元素定位和交互

2. **任务执行引擎**
   - 任务解析和执行
   - 错误处理和重试
   - 执行状态反馈

3. **队列管理**
   - Redis任务队列
   - 并发控制
   - 优先级调度

### 第三阶段：企业级功能
1. **权限管理**
   - 用户角色系统
   - 操作权限控制
   - 审计日志

2. **监控报表**
   - 执行统计分析
   - 性能监控
   - 错误报告

3. **高可用部署**
   - 负载均衡
   - 故障转移
   - 数据备份

## 🤝 贡献指南

1. **Fork项目**
2. **创建功能分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送分支** (`git push origin feature/AmazingFeature`)
5. **创建Pull Request**

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目维护者：[您的姓名]
- 邮箱：[您的邮箱]
- 项目地址：[GitHub仓库地址]

---

**当前状态**: 客户端插件已完成 ✅ | 服务端开发即将开始 🚀
