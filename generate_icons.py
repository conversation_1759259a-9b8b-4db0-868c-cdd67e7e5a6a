#!/usr/bin/env python3
"""
生成Chrome插件所需的不同尺寸图标
需要安装: pip install Pillow cairosvg
"""

import os
from PIL import Image

def generate_icons():
    """从SVG生成不同尺寸的PNG图标"""

    # 定义需要的图标尺寸
    sizes = [16, 32, 48, 128]

    # SVG文件路径
    svg_path = "icons/icon.svg"

    if not os.path.exists(svg_path):
        print(f"错误: 找不到SVG文件 {svg_path}")
        return

    # 确保icons目录存在
    os.makedirs("icons", exist_ok=True)

    try:
        # 为每个尺寸生成PNG
        for size in sizes:
            png_path = f"icons/icon{size}.png"

            # 将SVG转换为PNG
            cairosvg.svg2png(
                url=svg_path,
                write_to=png_path,
                output_width=size,
                output_height=size
            )

            print(f"生成图标: {png_path} ({size}x{size})")

        print("所有图标生成完成!")

    except ImportError:
        print("错误: 缺少必要的库")
        print("请运行: pip install Pillow cairosvg")
    except Exception as e:
        print(f"生成图标时出错: {e}")

def create_simple_icons():
    """如果无法使用SVG，创建简单的PNG图标"""

    sizes = [16, 32, 48, 128]

    # 确保icons目录存在
    os.makedirs("icons", exist_ok=True)

    try:
        from PIL import Image, ImageDraw, ImageFont

        for size in sizes:
            # 创建图像
            img = Image.new('RGBA', (size, size), (76, 175, 80, 255))
            draw = ImageDraw.Draw(img)

            # 绘制圆形背景
            margin = size // 10
            draw.ellipse([margin, margin, size-margin, size-margin],
                        fill=(76, 175, 80, 255), outline=(69, 160, 73, 255))

            # 绘制机器人图标（简化版）
            center = size // 2

            # 机器人头部
            head_size = size // 3
            head_x = center - head_size // 2
            head_y = center - head_size // 2 - size // 8
            draw.rectangle([head_x, head_y, head_x + head_size, head_y + head_size // 2],
                          fill=(255, 255, 255, 255))

            # 机器人眼睛
            eye_size = max(1, size // 20)
            eye_y = head_y + head_size // 6
            draw.ellipse([head_x + head_size // 4 - eye_size, eye_y,
                         head_x + head_size // 4 + eye_size, eye_y + eye_size * 2],
                        fill=(76, 175, 80, 255))
            draw.ellipse([head_x + 3 * head_size // 4 - eye_size, eye_y,
                         head_x + 3 * head_size // 4 + eye_size, eye_y + eye_size * 2],
                        fill=(76, 175, 80, 255))

            # 机器人身体
            body_width = head_size
            body_height = head_size // 2
            body_x = center - body_width // 2
            body_y = head_y + head_size // 2 + size // 20
            draw.rectangle([body_x, body_y, body_x + body_width, body_y + body_height],
                          fill=(255, 255, 255, 255))

            # 保存图标
            png_path = f"icons/icon{size}.png"
            img.save(png_path, 'PNG')
            print(f"生成简单图标: {png_path} ({size}x{size})")

        print("简单图标生成完成!")

    except ImportError:
        print("错误: 缺少PIL库")
        print("请运行: pip install Pillow")
    except Exception as e:
        print(f"生成简单图标时出错: {e}")

if __name__ == "__main__":
    print("正在生成Chrome插件图标...")

    # 直接使用简单图标生成方法
    create_simple_icons()
