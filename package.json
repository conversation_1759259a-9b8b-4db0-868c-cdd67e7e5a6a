{"name": "b-assistant-apa", "version": "1.0.0", "description": "B端智能助手-APA 完整RPA解决方案，包含Chrome插件客户端和Java服务端", "main": "index.js", "scripts": {"client:install": "echo 'Chrome Extension - Load client/ directory in Chrome'", "client:test": "open client/test.html", "server:init": "cd server && mvn clean install", "server:run": "cd server && mvn spring-boot:run", "server:test": "cd server && mvn test", "docs:serve": "echo 'Open docs/ directory to view documentation'", "build": "echo 'Build both client and server components'", "test": "npm run client:test && npm run server:test", "dev": "echo 'Development mode: Load client extension and start server'"}, "keywords": ["rpa", "automation", "chrome-extension", "java", "spring-boot", "selenium", "websocket", "assistant", "b2b", "enterprise"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/b-assistant-apa.git"}, "devDependencies": {"eslint": "^8.0.0", "prettier": "^2.0.0"}, "engines": {"node": ">=14.0.0"}, "workspaces": ["client", "server"], "directories": {"client": "client", "server": "server", "docs": "docs"}}