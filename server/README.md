# B端智能助手-APA 服务端

## 🚀 服务端概述

B端智能助手-APA服务端是RPA自动化执行引擎，负责接收客户端任务请求，执行浏览器自动化操作，并返回执行结果。

## 🎯 核心功能 (规划中)

### 1. WebSocket通信服务
- 与客户端插件实时双向通信
- 消息路由和广播
- 连接状态管理
- 心跳检测和断线重连

### 2. RPA执行引擎
- 浏览器自动化操作
- 模拟用户点击、输入、导航
- 页面元素识别和交互
- 数据抓取和处理

### 3. 任务调度系统
- 任务队列管理
- 并发控制和资源分配
- 优先级调度
- 错误重试机制

### 4. API接口服务
- RESTful API接口
- 任务CRUD操作
- 文件上传下载
- 用户认证授权

### 5. 数据处理模块
- Excel文件解析
- 数据格式转换
- 结果数据输出
- 报表生成

## 📁 项目结构 (规划)

```
server/
├── src/
│   ├── main/
│   │   ├── java/com/apa/
│   │   │   ├── controller/     # REST控制器
│   │   │   ├── service/        # 业务服务层
│   │   │   ├── entity/         # 数据实体
│   │   │   ├── repository/     # 数据访问层
│   │   │   ├── websocket/      # WebSocket处理
│   │   │   ├── rpa/            # RPA执行引擎
│   │   │   ├── scheduler/      # 任务调度器
│   │   │   ├── config/         # 配置类
│   │   │   └── ApaApplication.java
│   │   └── resources/
│   │       ├── application.yml # 应用配置
│   │       ├── static/         # 静态资源
│   │       └── templates/      # 模板文件
│   └── test/
│       └── java/               # 测试代码
├── config/
│   ├── docker/                 # Docker配置
│   ├── nginx/                  # Nginx配置
│   └── database/               # 数据库脚本
├── scripts/
│   ├── build.sh               # 构建脚本
│   ├── deploy.sh              # 部署脚本
│   └── start.sh               # 启动脚本
├── docs/
│   ├── api.md                 # API文档
│   ├── deployment.md          # 部署文档
│   └── development.md         # 开发文档
├── pom.xml                    # Maven配置
└── README.md                  # 本文件
```

## 🛠️ 技术栈 (规划)

### 后端框架
- **Spring Boot 3.x**: 主要应用框架
- **Spring WebSocket**: WebSocket支持
- **Spring Data JPA**: 数据访问层
- **Spring Security**: 安全认证
- **Spring Cache**: 缓存支持

### 数据存储
- **MySQL/PostgreSQL**: 主数据库
- **Redis**: 缓存和任务队列
- **MongoDB**: 日志和文档存储（可选）

### RPA技术
- **Selenium WebDriver**: 浏览器自动化
- **Chrome DevTools Protocol**: 高级浏览器控制
- **Playwright**: 现代浏览器自动化（备选）

### 其他组件
- **Apache POI**: Excel文件处理
- **Jackson**: JSON处理
- **Logback**: 日志框架
- **Micrometer**: 监控指标
- **Docker**: 容器化部署

## 🚀 开发计划

### 第一阶段：基础架构 (Week 1-2)
- [ ] Spring Boot项目初始化
- [ ] 数据库设计和建表
- [ ] 基础配置和依赖管理
- [ ] WebSocket服务搭建
- [ ] 基础API接口

### 第二阶段：核心功能 (Week 3-4)
- [ ] RPA执行引擎开发
- [ ] Selenium集成和封装
- [ ] 任务队列和调度器
- [ ] 文件上传下载处理
- [ ] 用户认证和权限

### 第三阶段：业务逻辑 (Week 5-6)
- [ ] 任务类型定义和处理
- [ ] Excel模板解析
- [ ] 数据处理和转换
- [ ] 执行结果反馈
- [ ] 错误处理和重试

### 第四阶段：优化完善 (Week 7-8)
- [ ] 性能优化和监控
- [ ] 日志和审计功能
- [ ] 单元测试和集成测试
- [ ] 文档编写
- [ ] 部署和运维

## 📋 API设计 (规划)

### WebSocket接口
```javascript
// 连接地址
ws://localhost:8080/websocket

// 消息格式
{
  "type": "chat|task_update|notification",
  "data": {...},
  "timestamp": 1642234567890
}
```

### REST API接口
```http
# 任务管理
POST   /api/tasks              # 创建任务
GET    /api/tasks              # 查询任务列表
GET    /api/tasks/{id}         # 获取任务详情
PUT    /api/tasks/{id}         # 更新任务
DELETE /api/tasks/{id}         # 删除任务

# 文件处理
POST   /api/files/upload       # 文件上传
GET    /api/files/{id}         # 文件下载
POST   /api/files/parse        # 文件解析

# 用户管理
POST   /api/auth/login         # 用户登录
POST   /api/auth/logout        # 用户登出
GET    /api/users/profile      # 用户信息

# 系统管理
GET    /api/system/status      # 系统状态
GET    /api/system/config      # 系统配置
```

## 🗄️ 数据库设计 (规划)

### 核心表结构
```sql
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 任务表
CREATE TABLE tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING',
    user_id BIGINT,
    config JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 任务执行记录表
CREATE TABLE task_executions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id BIGINT,
    status VARCHAR(20),
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    result JSON,
    error_message TEXT
);

-- 文件表
CREATE TABLE files (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255),
    original_name VARCHAR(255),
    file_size BIGINT,
    content_type VARCHAR(100),
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 配置管理 (规划)

### application.yml
```yaml
server:
  port: 8080

spring:
  datasource:
    url: **********************************
    username: ${DB_USERNAME:apa_user}
    password: ${DB_PASSWORD:apa_pass}
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false

apa:
  websocket:
    endpoint: /websocket
    allowed-origins: "*"
  
  rpa:
    browser: chrome
    headless: true
    timeout: 30000
  
  file:
    upload-dir: ./uploads
    max-size: 10MB
```

## 🐳 部署方案 (规划)

### Docker部署
```dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app
COPY target/apa-server.jar app.jar

EXPOSE 8080
ENTRYPOINT ["java", "-jar", "app.jar"]
```

### Docker Compose
```yaml
version: '3.8'
services:
  apa-server:
    build: .
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
  
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: apa_db
      MYSQL_USER: apa_user
      MYSQL_PASSWORD: apa_pass
      MYSQL_ROOT_PASSWORD: root_pass
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
```

## 🧪 测试策略 (规划)

### 单元测试
- Service层业务逻辑测试
- Repository层数据访问测试
- RPA引擎功能测试

### 集成测试
- API接口测试
- WebSocket通信测试
- 数据库集成测试

### 端到端测试
- 完整业务流程测试
- 客户端与服务端集成测试
- 性能和压力测试

## 📊 监控和运维 (规划)

### 应用监控
- Spring Boot Actuator健康检查
- Micrometer指标收集
- 自定义业务指标

### 日志管理
- 结构化日志输出
- 日志级别动态调整
- 日志聚合和分析

### 性能监控
- JVM性能监控
- 数据库连接池监控
- Redis缓存命中率

## 🔐 安全考虑 (规划)

### 认证授权
- JWT Token认证
- 角色权限控制
- API接口鉴权

### 数据安全
- 敏感数据加密
- SQL注入防护
- XSS攻击防护

### 网络安全
- HTTPS强制使用
- CORS跨域配置
- 请求频率限制

## 📈 性能优化 (规划)

### 缓存策略
- Redis缓存热点数据
- 本地缓存配置
- 缓存失效策略

### 数据库优化
- 索引优化
- 查询优化
- 连接池配置

### 并发处理
- 线程池配置
- 异步任务处理
- 资源限制控制

---

**当前状态**: 🔄 规划阶段，即将开始开发  
**预计完成**: 8周开发周期  
**技术负责人**: [待分配]
